<?php

namespace App\Modules\UserDomainExport\Services;

use App\Events\ClientActivityEvent;
use App\Http\Resources\UserDomainExport\UserDomainExportCollection;
use App\Models\UserDomainExport;
use App\Modules\Cart\Services\UserCart;
use App\Modules\Histories\Constants\UserTransactionType;
use App\Modules\UserDomainExport\Jobs\UserDomainExportFileGenerationJob;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\StreamedResponse;
use ZipArchive;
use Illuminate\Support\Facades\Response;

class UserDomainExportService
{
    /**
     * Class Constructor
     *
     * @return void
     */
    public function __construct()
    {
        // ...
    }

    /**
     * Process List Data
     *
     * @param  int  $userId
     */
    public function processListData(array $data, string $userId): array
    {
        $items = $this->fetchItemsWithPagination(
            $data,
            $userId
        );

        $cart = new UserCart;
        $cartCount = $cart->getTotalDomain();

        return compact('items', 'cartCount');
    }

    /**
     * Build Query
     */
    public function buildQuery(Builder $query, array $data, int $userId): Builder
    {
        $query->when(
            isset($userId),
            function (Builder $q) use ($userId) {
                $q->where('generated_by_user_id', '=', $userId);
            }
        )
            ->when(
                isset($data['selectedItems']),
                function (Builder $q) use ($data) {
                    $q->whereIn('id', $data['selectedItems']);
                }
            );

        return $query;
    }

    /**
     * Fetch Items Count
     *
     * @param  bool  $isArchived  = false
     */
    public function fetchItemsCount(array $data, int $userId, bool $isArchived = false): int
    {
        $query = UserDomainExport::query()
            ->where('generated_by_user_id', '=', $userId);

        if ($isArchived == true) {
            $query = $query->onlyTrashed();
        }

        $items = $query->count();

        return $items;
    }

    /**
     * Fetch Items
     */
    public function fetchItemsWithPagination(array $data, int $userId): UserDomainExportCollection
    {
        $sortColumn = 'id';
        $sortDirection = 'desc';
        $showItems = $data['showItems'] ?? 20;

        if (isset($data['sortColumn']) && isset($data['isSortDesc'])) {
            $sortColumn = $data['sortColumn'];
            $sortDirection = $data['isSortDesc'] == true ? 'desc' : 'asc';
        }

        $query = UserDomainExport::query();
        $query = $this->buildQuery($query, $data, $userId);
        $items = $query->orderBy($sortColumn, $sortDirection)->cursorPaginate($showItems)->withQueryString();

        return (new UserDomainExportCollection($items))
            ->additional(
                [
                    'meta' => [
                        'total' => $this->fetchItemsCount($data, $userId),
                    ],
                ]
            );
    }

    /**
     * Store File
     */
    public function generateFile(array $data, int $userId): void
    {
        $currentDate = Carbon::now()->format('Y-m-d-His');
        $fileName = "domains_{$currentDate}.csv";

        $userDomainExport = UserDomainExport::create(
            [
                'name' => $fileName,
                'generated_by_user_id' => $userId,
            ]
        );

        UserDomainExportFileGenerationJob::dispatch($data, $userDomainExport->id, $userId);

    }

    /**
     * Download File
     */
    public function downloadFile(int $id): StreamedResponse
    {
        $userDomainExport = UserDomainExport::findOrFail($id);

        $userDomainExport->last_downloaded_at = Carbon::now();
        $userDomainExport->save();

        // Ensure the file exists in the storage location
        if (!Storage::disk('public')->exists($userDomainExport->path)) {
            abort(404, 'File not found.');
        }

        // dd(Storage::disk('public')->download($userDomainExport->path, $userDomainExport->name));

        return Storage::disk('public')->download($userDomainExport->path, $userDomainExport->name);
    }

    /**
     * Delete File
     */
    public function deleteFile(int $id): void
    {
        $item = UserDomainExport::findOrFail($id);

        if (Storage::disk('public')->exists($item->path)) {
            Storage::disk('public')->delete($item->path);
        }

        $item->delete();

        $payload = [
            'export_id' => $item->id,
            'file_name' => $item->name,
            'deleted_at' => Carbon::now()->toIso8601String(),
        ];

        event(new ClientActivityEvent(
            $item->generated_by_user_id,
            UserTransactionType::DOMAIN_UPDATE,
            'Successfully removed domain export file: ' . $item->name,
            '',
            $payload
        ));
    }

    /**
     * Batch Delete
     */
    public function batchDelete(array $data, int $userId): void
    {
        $query = UserDomainExport::query();
        $query = $this->buildQuery($query, $data, $userId);
        $items = $query->get();

        foreach ($items as $item) {
            if ($item->path == null) {
                continue;
            }

            if (Storage::disk('public')->exists($item->path)) {
                Storage::disk('public')->delete($item->path);
            }
        }

        UserDomainExport::destroy($items->pluck('id'));

        $payload = [
            'export_id' => $item->id,
            'file_name' => $item->name,
            'deleted_at' => Carbon::now()->toIso8601String(),
        ];

        event(new ClientActivityEvent(
            $userId,
            UserTransactionType::DOMAIN_UPDATE,
            'Successfully removed multiple domain export files (' . $items->count() . ' files)',
            '',
            $payload
        ));
    }

    /**
     * Clear Items
     */
    public function clearItems(int $userId): void
    {
        $query = UserDomainExport::select(['id', 'path']);
        $query = $this->buildQuery($query, [], $userId);

        $items = $query->get();

        foreach ($items as $item) {
            if ($item->path == null) {
                continue;
            }

            if (Storage::disk('public')->exists($item->path)) {
                Storage::disk('public')->delete($item->path);
            }
        }

        UserDomainExport::destroy($items->pluck('id'));
    }


    /**
     * Download selected export files as zip
     */
    public function downloadBatch(array $ids, int $userId)
    {
        $filePaths = UserDomainExport::query()
            ->whereIn('id', $ids)
            ->where('generated_by_user_id', $userId)
            ->pluck('path')
            ->toArray();

        if (count($filePaths) == 0) 
        {
            abort(404, 'No files found to download.');
        }

        return $this->createZipResponse(
            $filePaths, 
            'domain_exports_' . Carbon::now()->format('Y-m-d-His') . '.zip'
        );
    }

    /**
     * Download all export files as zip
     */
    public function downloadAll(int $userId)
    {
        $filePaths = UserDomainExport::query()
            ->where('generated_by_user_id', $userId)->get()
            ->pluck('path')
            ->toArray();
        
        if (count($filePaths) == 0) 
        {
            abort(404, 'No files found to download.');
        }

        return $this->createZipResponse(
            $filePaths, 
            'domain_exports_all_' . Carbon::now()->format('Y-m-d-His') . '.zip'
        );
    }

    /**
     * Create zip archive from multiple export files
     */
    private function createZipResponse(array $filePaths, string $zipFileName)
    {
        $zipRelativePath = 'temp/' . $zipFileName;
        $zipAbsolutePath = Storage::disk('public')->path($zipRelativePath);

        // Ensure 'temp' directory exists
        Storage::disk('public')->makeDirectory('temp');

        $zip = new ZipArchive;

        if ($zip->open($zipAbsolutePath, ZipArchive::CREATE | ZipArchive::OVERWRITE) === true) 
        {
            foreach ($filePaths as $filePath) 
            {
                if (Storage::disk('public')->exists($filePath)) 
                {
                    $fileContents  = Storage::disk('public')->get($filePath); // ✅ FIXED
                    $filenameInZip = basename($filePath);

                    $zip->addFromString($filenameInZip, $fileContents);
                }
            }

            $zip->close();
        } 
        else 
        {
            return response()->json(['error' => 'Could not create ZIP file'], 500);
        }

        // ✅ Download file from the disk correctly
        return response()->download($zipAbsolutePath)->deleteFileAfterSend(true);
    }
}
